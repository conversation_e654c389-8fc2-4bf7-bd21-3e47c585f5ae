namespace LibraryManagementSystem.DAL.Models.DTOs
{
    /// <summary>
    /// Data Transfer Object for book search operations
    /// </summary>
    public class BookSearchDto
    {
        public string? Title { get; set; }
        public string? Author { get; set; }
        public string? ISBN { get; set; }
        public string? Genre { get; set; }
        public bool? IsAvailable { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string SortBy { get; set; } = "Title";
        public bool SortDescending { get; set; } = false;
    }
    
    /// <summary>
    /// Data Transfer Object for paginated search results
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
    
    /// <summary>
    /// Data Transfer Object for borrowing operations
    /// </summary>
    public class BorrowingDto
    {
        public int UserId { get; set; }
        public int BookId { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Notes { get; set; }
    }
    
    /// <summary>
    /// Data Transfer Object for return operations
    /// </summary>
    public class ReturnDto
    {
        public int BorrowingId { get; set; }
        public DateTime ReturnDate { get; set; } = DateTime.Now;
        public decimal? LateFee { get; set; }
        public string? Notes { get; set; }
    }
}
