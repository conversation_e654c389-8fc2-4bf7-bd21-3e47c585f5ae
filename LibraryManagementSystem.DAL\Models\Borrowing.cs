using System.ComponentModel.DataAnnotations;

namespace LibraryManagementSystem.DAL.Models
{
    /// <summary>
    /// Represents a borrowing transaction in the library system
    /// </summary>
    public class Borrowing
    {
        public int BorrowingId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        public int BookId { get; set; }
        
        public DateTime BorrowDate { get; set; }
        
        public DateTime DueDate { get; set; }
        
        public DateTime? ReturnDate { get; set; }
        
        public bool IsReturned { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal LateFee { get; set; }
        
        [StringLength(200)]
        public string? Notes { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime ModifiedDate { get; set; }
        
        // Navigation properties (for display purposes)
        public User? User { get; set; }
        
        public Book? Book { get; set; }
        
        // Computed properties
        public bool IsOverdue => !IsReturned && DateTime.Now > DueDate;
        
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate).Days : 0;
        
        public int DaysRemaining => IsReturned ? 0 : Math.Max(0, (DueDate - DateTime.Now).Days);
        
        public string Status => IsReturned ? "Returned" : (IsOverdue ? "Overdue" : "Active");
        
        public string BorrowingPeriod => $"{BorrowDate:MMM dd, yyyy} - {(IsReturned ? ReturnDate?.ToString("MMM dd, yyyy") : DueDate.ToString("MMM dd, yyyy"))}";
    }
}
