using System.ComponentModel.DataAnnotations;

namespace LibraryManagementSystem.DAL.Models
{
    /// <summary>
    /// Represents a book in the library system
    /// </summary>
    public class Book
    {
        public int BookId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Author { get; set; } = string.Empty;
        
        [Required]
        [StringLength(20)]
        public string ISBN { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? Publisher { get; set; }
        
        [Range(1000, 9999)]
        public int? PublicationYear { get; set; }
        
        [StringLength(50)]
        public string? Genre { get; set; }
        
        [Range(0, int.MaxValue)]
        public int TotalCopies { get; set; }
        
        [Range(0, int.MaxValue)]
        public int AvailableCopies { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime ModifiedDate { get; set; }
        
        // Computed properties
        public bool IsAvailable => AvailableCopies > 0;
        
        public int BorrowedCopies => TotalCopies - AvailableCopies;
        
        public string DisplayTitle => $"{Title} by {Author}";
        
        public string AvailabilityStatus => IsAvailable 
            ? $"Available ({AvailableCopies} of {TotalCopies})" 
            : "Not Available";
    }
}
