using Microsoft.Data.SqlClient;
using System.Data;

namespace LibraryManagementSystem.DAL.Data
{
    /// <summary>
    /// Factory for creating database connections
    /// </summary>
    public interface IDatabaseConnectionFactory
    {
        IDbConnection CreateConnection();
        Task<IDbConnection> CreateConnectionAsync();
    }
    
    /// <summary>
    /// Implementation of database connection factory using SQL Server
    /// </summary>
    public class DatabaseConnectionFactory : IDatabaseConnectionFactory
    {
        private readonly string _connectionString;
        
        public DatabaseConnectionFactory(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }
        
        public IDbConnection CreateConnection()
        {
            var connection = new SqlConnection(_connectionString);
            connection.Open();
            return connection;
        }
        
        public async Task<IDbConnection> CreateConnectionAsync()
        {
            var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            return connection;
        }
    }
    
    /// <summary>
    /// Database helper class for common operations
    /// </summary>
    public static class DatabaseHelper
    {
        /// <summary>
        /// Executes a scalar query and returns the result
        /// </summary>
        public static async Task<T?> ExecuteScalarAsync<T>(IDbConnection connection, string sql, object? parameters = null)
        {
            using var command = connection.CreateCommand();
            command.CommandText = sql;
            
            if (parameters != null)
            {
                AddParameters(command, parameters);
            }
            
            var result = await ((SqlCommand)command).ExecuteScalarAsync();
            return result == null || result == DBNull.Value ? default(T) : (T)result;
        }
        
        /// <summary>
        /// Executes a non-query command and returns the number of affected rows
        /// </summary>
        public static async Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql, object? parameters = null)
        {
            using var command = connection.CreateCommand();
            command.CommandText = sql;
            
            if (parameters != null)
            {
                AddParameters(command, parameters);
            }
            
            return await ((SqlCommand)command).ExecuteNonQueryAsync();
        }
        
        /// <summary>
        /// Executes a query and returns a data reader
        /// </summary>
        public static async Task<IDataReader> ExecuteReaderAsync(IDbConnection connection, string sql, object? parameters = null)
        {
            var command = connection.CreateCommand();
            command.CommandText = sql;
            
            if (parameters != null)
            {
                AddParameters(command, parameters);
            }
            
            return await ((SqlCommand)command).ExecuteReaderAsync();
        }
        
        /// <summary>
        /// Adds parameters to a command from an anonymous object
        /// </summary>
        private static void AddParameters(IDbCommand command, object parameters)
        {
            var properties = parameters.GetType().GetProperties();
            foreach (var property in properties)
            {
                var parameter = command.CreateParameter();
                parameter.ParameterName = $"@{property.Name}";
                parameter.Value = property.GetValue(parameters) ?? DBNull.Value;
                command.Parameters.Add(parameter);
            }
        }
        
        /// <summary>
        /// Maps a data reader to an object using reflection
        /// </summary>
        public static T MapToObject<T>(IDataReader reader) where T : new()
        {
            var obj = new T();
            var properties = typeof(T).GetProperties();
            
            for (int i = 0; i < reader.FieldCount; i++)
            {
                var columnName = reader.GetName(i);
                var property = properties.FirstOrDefault(p => 
                    string.Equals(p.Name, columnName, StringComparison.OrdinalIgnoreCase));
                
                if (property != null && !reader.IsDBNull(i))
                {
                    var value = reader.GetValue(i);
                    if (value != DBNull.Value)
                    {
                        // Handle nullable types
                        var targetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
                        var convertedValue = Convert.ChangeType(value, targetType);
                        property.SetValue(obj, convertedValue);
                    }
                }
            }
            
            return obj;
        }
    }
}
